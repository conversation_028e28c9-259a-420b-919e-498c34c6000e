<resources>
    <string name="app_name">VideoIMUCapture</string>
    <string name="toggleRecordingOn">Record</string>
    <string name="toggleRecordingOff">Stop</string>
    <string name="tfcCameraParams">Camera params: %1$dx%2$d @%3$.1f fps</string>
    <string name="title_activity_settings">SettingsActivity</string>
    <string name="camera_page_title">VideoIMUCapture</string>
    <string name="settings_page_title">Settings</string>
    <string name="settings_toolbar">Settings</string>
    <string name="back_toolbar">Back</string>

    <string name="record_description">Toggle recording</string>
    <string name="info_toolbar">Information</string>
    <string name="info_dialog_title">Information</string>
    <string name="info_dialog_message">Records Video, IMU and Camera Meta Data to this location:\n %s</string>
    <string name="info_dialog_button">OK</string>
    <string name="warning_icon_desc">Warning on Stabilization</string>
    <string name="warning_dialog_title">Warning</string>
    <string name="warning_text_ois_with_data">Optical Image Stabilization (OIS) is enabled, you need to compensate for this with the OIS sample data during 3D reconstruction.</string>
    <string name="warning_text_ois_no_data">Optical Image Stabilization (OIS) is enabled but OIS data is not stored, which means that the saved camera parameters are not valid. Please enable OIS data in settings.</string>
    <string name="warning_text_dvs">Digital Video Stabilization is enabled, the stored camera parameters will not be correct.</string>
    <string name="permission_text">This app requires permission to use the camera.\n\nIt will be useless otherwise.</string>
    <string name="grant_permission_button">Grant Permission</string>
    <string name="decline_permission_button">Keep it useless</string>
    <string name="warning_text_imu_missing">Cannot find IMU sensor</string>
    <string name="warning_text_distortion">Distrotion Correction enabled, distortion parameters are invalid.</string>
</resources>